#!/usr/bin/env python3
"""
CLI program to test the interviewer_agent functionality.
<PERSON><PERSON> interrupts by prompting user for input and continuing execution.
"""

import sys

from langgraph.checkpoint.memory import MemorySaver

from graph.interviewer_agent_graph import create_interviewer_graph
from langgraph.types import Command
from models.state import State
from models.workflowGraph import WorkflowGraph


def print_banner():
    """Print a welcome banner."""
    print("=" * 60)
    print("  Interviewer Agent CLI")
    print("=" * 60)
    print()


def get_example_prompt() -> str:
    """Return an example user prompt for workflow generation."""
    return "I want to create a workflow that processes customer feedback from multiple sources (email, social media, surveys) and generates automated responses and sentiment analysis reports."


def run_graph(user_prompt: str):
    """Run the interviewer agent graph with the given user prompt, handling interrupts."""
    print("\n" + "=" * 60)
    print(f"User Prompt: {user_prompt}")
    print("=" * 60)

    # Create checkpointer for state persistence
    checkpointer = MemorySaver()

    # Create the graph
    graph = create_interviewer_graph(checkpointer=checkpointer)

    # Initial state
    config = {"configurable": {"thread_id": "interviewer-thread-1"}}
    initial_workflow = WorkflowGraph(nodes={}, edges={})
    initial_workflow.add_node(
        node_id="start-node",
        label="Start",
        OriginalType="StartNode",
        type="component",
        position=(0, 0),
        parameters={},
    )

    initial_state = State(
        user_prompt=user_prompt,
        workflow_graph=initial_workflow,
        main_agent_messages=[],
        planner_messages=[],
        workflow_generation_messages=[],
        task_based_generation_messages=[],
        interviewer_messages=[],
    )

    print("\nStarting interview loop...")
    print("-" * 60)

    # Execute the interviewer graph with interrupt handling
    try:
        # Start the interview loop
        current_state = initial_state

        while True:
            try:
                # Invoke the graph and handle interrupts
                result = graph.invoke(current_state, config=config)

                # Interview completed successfully
                print("\n✅ Interview completed!")
                if "__interrupt__" not in result:
                    print(result)
                    break

                # Get the current state to see what question was asked
                ques = result["__interrupt__"][0].value
                print(f"\n🤖 Interviewer: {ques}")
                user_response = input("\n👤 Your response: ").strip()
                current_state = Command(
                    resume=user_response,
                    goto="ask_user",
                )
            except Exception as e:
                print(f"\n[ERROR] Graph execution failed: {e}")
                import traceback

                traceback.print_exc()

    except KeyboardInterrupt:
        print("\n\n⚠️ Interview interrupted by user.")
    except Exception as e:
        print(f"\n[ERROR] Graph execution failed: {e}")
        import traceback

        traceback.print_exc()


def main():
    """Main CLI function."""
    print_banner()

    print("Enter your workflow requirements or idea:")
    print("\nExample:")
    print(f'  "{get_example_prompt()}"')
    print("\nOr press Enter to use the example above:")

    user_input = input().strip()

    if user_input == "":
        user_prompt = get_example_prompt()
        print("\nUsing example prompt")
    else:
        user_prompt = user_input

    run_graph(user_prompt)


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nInterrupted by user. Goodbye!")
        sys.exit(0)
