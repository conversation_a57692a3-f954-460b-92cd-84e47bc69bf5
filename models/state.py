import operator
from operator import add, or_
from typing import Annotated, Any, Dict, List, Literal, Optional

from langchain_core.messages import AnyMessage
from langgraph.graph.message import add_messages
from pydantic import BaseModel, Field

from models.workflowGraph import WorkflowGraph
from utils.reducer import todo_reducer


class State(BaseModel):
    main_agent_messages: Annotated[List[AnyMessage], add_messages] = []
    planner_messages: Annotated[List[AnyMessage], add_messages] = []
    workflow_generation_messages: Annotated[List[AnyMessage], add_messages] = []
    task_based_generation_messages: Annotated[List[AnyMessage], add_messages] = []
    interviewer_messages: Annotated[List[AnyMessage], add_messages] = []

    plan: Optional[str] = None
    requirement: Optional[str] = None

    # Interviewer agent specific fields
    user_prompt: Optional[str] = None  

    workflow_graph: WorkflowGraph
    todo: Annotated[List[Dict[str, Any]], todo_reducer] = []

    # test node graph
    nodes_to_test: Annotated[List[Dict], add] = []
    mcps_infos: Annotated[Dict, or_] = {}
    verified_mcp_data: Annotated[Dict[str, Dict], or_] = {}
    dummy_data: str = ""
    unauthorized_mcps: List[Dict[str, Any]] = []
    output: str = ""
